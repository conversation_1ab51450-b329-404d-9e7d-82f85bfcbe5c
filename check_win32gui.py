import win32gui
import sys

print(f"win32gui module loaded from: {win32gui.__file__}")
print(f"Python version: {sys.version}")

# Para verificar se o pywin32 está instalado corretamente e qual versão
try:
    import win32api
    print(f"win32api module loaded from: {win32api.__file__}")

    # Tentar obter a versão do pywin32 usando pip
    try:
        import subprocess
        result = subprocess.run(['pip', 'show', 'pywin32'], capture_output=True, text=True)
        if result.returncode == 0:
            for line in result.stdout.split('\n'):
                if line.startswith('Version:'):
                    version = line.split(':', 1)[1].strip()
                    print(f"pywin32 version: {version}")
                    break
        else:
            print("Could not determine pywin32 version using pip show")
    except Exception as e:
        print(f"Could not determine pywin32 version: {e}")

except ImportError:
    print("win32api module (part of pywin32) could not be imported.")


print("\n--- win32gui attributes ---")
# Listar alguns atributos para ver se o módulo parece correto
# for attr in dir(win32gui)[:20]: # Primeiros 20 para não poluir muito
#    print(attr)

if hasattr(win32gui, 'PrintWindow'):
    print("\nSUCCESS: win32gui.PrintWindow IS available.")
else:
    print("\nERROR: win32gui.PrintWindow IS NOT available.")
    print("Functions in win32gui starting with 'Print' (case-insensitive):")
    found_print_functions = False
    for attr in dir(win32gui):
        if attr.lower().startswith('print'):
            print(f"  - {attr}")
            found_print_functions = True
    if not found_print_functions:
        print("  (No functions starting with 'Print' found)")
