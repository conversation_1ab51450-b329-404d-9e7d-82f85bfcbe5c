# Script para iniciar o MT4 Extractor
# Autor: Sistema de Extração MT4
# Data: 2025-01-26

Write-Host "🚀 Iniciando MT4 Extractor..." -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Cyan

# Verificar se o ambiente virtual existe
if (-not (Test-Path ".\.venv\Scripts\python.exe")) {
    Write-Host "❌ Ambiente virtual não encontrado!" -ForegroundColor Red
    Write-Host "Execute: python -m venv .venv" -ForegroundColor Yellow
    Write-Host "Depois: .\.venv\Scripts\activate" -ForegroundColor Yellow
    Write-Host "E instale: pip install -r requirements.txt" -ForegroundColor Yellow
    pause
    exit 1
}

# Verificar se o arquivo extractor.py existe
if (-not (Test-Path ".\extractor.py")) {
    Write-Host "❌ Arquivo extractor.py não encontrado!" -ForegroundColor Red
    pause
    exit 1
}

# Mostrar informações do sistema
Write-Host "📊 Informações do Sistema:" -ForegroundColor Yellow
Write-Host "- Modelo: Gemini 2.5 Flash-Lite Preview" -ForegroundColor White
Write-Host "- Intervalo: A cada 5 minutos sincronizado" -ForegroundColor White
Write-Host "- Arquivos CSV: D1_1.csv, H4_1.csv, H4_2.csv, H4_3.csv" -ForegroundColor White
Write-Host "- Horários: :00, :05, :10, :15, :20, :25, :30, :35, :40, :45, :50, :55" -ForegroundColor White
Write-Host ""

# Verificar se o MT4 está rodando
$mt4Process = Get-Process -Name "terminal" -ErrorAction SilentlyContinue
if ($mt4Process) {
    Write-Host "✅ MT4 detectado rodando (PID: $($mt4Process.Id))" -ForegroundColor Green
} else {
    Write-Host "⚠️  MT4 não detectado. Certifique-se de que está rodando!" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🔄 Iniciando extração contínua..." -ForegroundColor Green
Write-Host "💡 Para parar: Pressione Ctrl+C" -ForegroundColor Yellow
Write-Host "=================================" -ForegroundColor Cyan
Write-Host ""

# Salvar PID do processo em arquivo para controle
$pidFile = ".\extractor.pid"

try {
    # Iniciar o extractor
    $process = Start-Process -FilePath ".\.venv\Scripts\python.exe" -ArgumentList ".\extractor.py" -PassThru -NoNewWindow
    
    # Salvar PID
    $process.Id | Out-File -FilePath $pidFile -Encoding UTF8
    
    Write-Host "✅ Extractor iniciado com PID: $($process.Id)" -ForegroundColor Green
    Write-Host "📁 PID salvo em: $pidFile" -ForegroundColor Gray
    Write-Host ""
    Write-Host "🔍 Monitorando processo..." -ForegroundColor Yellow
    
    # Aguardar o processo terminar
    $process.WaitForExit()
    
} catch {
    Write-Host "❌ Erro ao iniciar o extractor: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    # Limpar arquivo PID
    if (Test-Path $pidFile) {
        Remove-Item $pidFile -Force
    }
    Write-Host ""
    Write-Host "🛑 Extractor finalizado." -ForegroundColor Red
}

Write-Host ""
Write-Host "Pressione qualquer tecla para sair..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
