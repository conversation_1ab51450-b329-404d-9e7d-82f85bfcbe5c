import ctypes
from ctypes import wintypes
import win32gui

# Tentar acessar PrintWindow diretamente via ctypes
user32 = ctypes.windll.user32

# Definir a função PrintWindow
try:
    PrintWindow = user32.PrintWindow
    PrintWindow.argtypes = [wintypes.HWND, wintypes.HDC, wintypes.UINT]
    PrintWindow.restype = wintypes.BOOL
    print("✅ PrintWindow está disponível via ctypes!")
    
    # Testar se win32gui tem PrintWindow
    if hasattr(win32gui, 'PrintWindow'):
        print("✅ PrintWindow também está disponível via win32gui!")
    else:
        print("❌ PrintWindow NÃO está disponível via win32gui")
        print("Mas podemos usar via ctypes!")
        
        # Adicionar PrintWindow ao módulo win32gui
        win32gui.PrintWindow = PrintWindow
        print("✅ PrintWindow adicionado ao win32gui!")
        
except Exception as e:
    print(f"❌ Erro ao acessar PrintWindow: {e}")

# Verificar novamente
print(f"\nVerificação final:")
print(f"hasattr(win32gui, 'PrintWindow'): {hasattr(win32gui, 'PrintWindow')}")

# Listar todas as funções que começam com Print
print("\nFunções no win32gui que começam com 'Print':")
for attr in dir(win32gui):
    if attr.lower().startswith('print'):
        print(f"  - {attr}")
