{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://connectors.googleapis.com/", "batchPath": "batch", "canonicalName": "Connectors", "description": "Enables users to create and manage connections to Google Cloud services and third-party business applications using the Connectors interface.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/apigee/docs/api-platform/connectors/about-connectors", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "connectors:v2", "kind": "discovery#restDescription", "mtlsRootUrl": "https://connectors.mtls.googleapis.com/", "name": "connectors", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"resources": {"connections": {"methods": {"checkReadiness": {"description": "Reports readiness status of the connector. Similar logic to GetStatus but modified for kubernetes health check to understand.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}:checkReadiness", "httpMethod": "GET", "id": "connectors.projects.locations.connections.checkReadiness", "parameterOrder": ["name"], "parameters": {"name": {"location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connections/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+name}:checkReadiness", "response": {"$ref": "CheckReadinessResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "checkStatus": {"description": "Reports the status of the connection. Note that when the connection is in a state that is not ACTIVE, the implementation of this RPC method must return a Status with the corresponding State instead of returning a gRPC status code that is not \"OK\", which indicates that ConnectionStatus itself, not the connection, failed.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}:checkStatus", "httpMethod": "GET", "id": "connectors.projects.locations.connections.checkStatus", "parameterOrder": ["name"], "parameters": {"name": {"location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connections/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+name}:checkStatus", "response": {"$ref": "CheckStatusResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "exchangeAuthCode": {"description": "ExchangeAuthCode exchanges the OAuth authorization code (and other necessary data) for an access token (and associated credentials).", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}:exchangeAuthCode", "httpMethod": "POST", "id": "connectors.projects.locations.connections.exchangeAuthCode", "parameterOrder": ["name"], "parameters": {"name": {"location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connections/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+name}:exchangeAuthCode", "request": {"$ref": "ExchangeAuthCodeRequest"}, "response": {"$ref": "ExchangeAuthCodeResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "executeSqlQuery": {"description": "Executes a SQL statement specified in the body of the request. An example of this SQL statement in the case of Salesforce connector would be 'select * from Account a, Order o where a.Id = o.AccountId'.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}:executeSqlQuery", "httpMethod": "POST", "id": "connectors.projects.locations.connections.executeSqlQuery", "parameterOrder": ["connection"], "parameters": {"connection": {"description": "Required. Resource name of the Connection. Format: projects/{project}/locations/{location}/connections/{connection}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connections/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+connection}:executeSqlQuery", "request": {"$ref": "ExecuteSqlQueryRequest"}, "response": {"$ref": "ExecuteSqlQueryResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "refreshAccessToken": {"description": "RefreshAccessToken exchanges the OAuth refresh token (and other necessary data) for a new access token (and new associated credentials).", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}:refreshAccessToken", "httpMethod": "POST", "id": "connectors.projects.locations.connections.refreshAccessToken", "parameterOrder": ["name"], "parameters": {"name": {"location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connections/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+name}:refreshAccessToken", "request": {"$ref": "RefreshAccessTokenRequest"}, "response": {"$ref": "RefreshAccessTokenResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"actions": {"methods": {"execute": {"description": "Executes an action with the name specified in the request. The input parameters for executing the action are passed through the body of the ExecuteAction request.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}/actions/{actionsId}:execute", "httpMethod": "POST", "id": "connectors.projects.locations.connections.actions.execute", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the Action. Format: projects/{project}/locations/{location}/connections/{connection}/actions/{action}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connections/[^/]+/actions/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+name}:execute", "request": {"$ref": "ExecuteActionRequest"}, "response": {"$ref": "ExecuteActionResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the schema of the given action.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}/actions/{actionsId}", "httpMethod": "GET", "id": "connectors.projects.locations.connections.actions.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the Action. Format: projects/{project}/locations/{location}/connections/{connection}/actions/{action}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connections/[^/]+/actions/[^/]+$", "required": true, "type": "string"}, "view": {"description": "Specified view of the action schema.", "enum": ["ACTION_SCHEMA_VIEW_UNSPECIFIED", "ACTION_SCHEMA_VIEW_BASIC", "ACTION_SCHEMA_VIEW_ENRICHED"], "enumDescriptions": ["VIEW_UNSPECIFIED. The unset value. Defaults to BASIC View.", "Return basic action schema.", "Return enriched action schema."], "location": "query", "type": "string"}}, "path": "v2/{+name}", "response": {"$ref": "Action"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Gets the schema of all the actions supported by the connector.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}/actions", "httpMethod": "GET", "id": "connectors.projects.locations.connections.actions.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Number of Actions to return. Defaults to 25.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Page token, return from a previous ListActions call, that can be used retrieve the next page of content. If unspecified, the request returns the first page of actions.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent resource name of the Action. Format: projects/{project}/locations/{location}/connections/{connection}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connections/[^/]+$", "required": true, "type": "string"}, "view": {"description": "Specifies which fields of the Action are returned in the response.", "enum": ["ACTION_VIEW_UNSPECIFIED", "ACTION_VIEW_BASIC", "ACTION_VIEW_FULL"], "enumDescriptions": ["VIEW_UNSPECIFIED. The unset value Defaults to FULL View.", "Return only action names.", "Return actions with schema."], "location": "query", "type": "string"}}, "path": "v2/{+parent}/actions", "response": {"$ref": "ListActionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "entityTypes": {"methods": {"get": {"description": "Gets metadata of given entity type", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}/entityTypes/{entityTypesId}", "httpMethod": "GET", "id": "connectors.projects.locations.connections.entityTypes.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the Entity Type. Format: projects/{project}/locations/{location}/connections/{connection}/entityTypes/{entityType}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connections/[^/]+/entityTypes/[^/]+$", "required": true, "type": "string"}, "view": {"description": "Specifies view for entity type schema.", "enum": ["ENTITY_TYPE_SCHEMA_VIEW_UNSPECIFIED", "ENTITY_TYPE_SCHEMA_VIEW_BASIC", "ENTITY_TYPE_SCHEMA_VIEW_ENRICHED"], "enumDescriptions": ["VIEW_UNSPECIFIED. The unset value. Defaults to BASIC View.", "Return basic entity type schema.", "Return enriched entity types schema."], "location": "query", "type": "string"}}, "path": "v2/{+name}", "response": {"$ref": "EntityType"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists metadata related to all entity types present in the external system.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}/entityTypes", "httpMethod": "GET", "id": "connectors.projects.locations.connections.entityTypes.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Number of entity types to return. Defaults to 25.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Page token, return from a previous ListEntityTypes call, that can be used retrieve the next page of content. If unspecified, the request returns the first page of entity types.", "location": "query", "type": "string"}, "parent": {"description": "Required. Resource name of the Entity Type. Format: projects/{project}/locations/{location}/connections/{connection}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connections/[^/]+$", "required": true, "type": "string"}, "view": {"description": "Specifies which fields of the Entity Type are returned in the response.", "enum": ["ENTITY_TYPE_VIEW_UNSPECIFIED", "ENTITY_TYPE_VIEW_BASIC", "ENTITY_TYPE_VIEW_FULL"], "enumDescriptions": ["VIEW_UNSPECIFIED. The unset value. Defaults to FULL View.", "Return only entity type names.", "Return entity types with schema"], "location": "query", "type": "string"}}, "path": "v2/{+parent}/entityTypes", "response": {"$ref": "ListEntityTypesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"entities": {"methods": {"create": {"description": "Creates a new entity row of the specified entity type in the external system. The field values for creating the row are contained in the body of the request. The response message contains a `Entity` message object returned as a response by the external system.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}/entityTypes/{entityTypesId}/entities", "httpMethod": "POST", "id": "connectors.projects.locations.connections.entityTypes.entities.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Resource name of the Entity Type. Format: projects/{project}/locations/{location}/connections/{connection}/entityTypes/{type}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connections/[^/]+/entityTypes/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+parent}/entities", "request": {"$ref": "Entity"}, "response": {"$ref": "Entity"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes an existing entity row matching the entity type and entity id specified in the request.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}/entityTypes/{entityTypesId}/entities/{entitiesId}", "httpMethod": "DELETE", "id": "connectors.projects.locations.connections.entityTypes.entities.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the Entity Type. Format: projects/{project}/locations/{location}/connections/{connection}/entityTypes/{type}/entities/{id}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connections/[^/]+/entityTypes/[^/]+/entities/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "deleteEntitiesWithConditions": {"description": "Deletes entities based on conditions specified in the request and not on entity id.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}/entityTypes/{entityTypesId}/entities:deleteEntitiesWithConditions", "httpMethod": "POST", "id": "connectors.projects.locations.connections.entityTypes.entities.deleteEntitiesWithConditions", "parameterOrder": ["entityType"], "parameters": {"conditions": {"description": "Required. Conditions to be used when deleting entities. From a proto standpoint, There are no restrictions on what can be passed using this field. The connector documentation should have information about what format of filters/conditions are supported. Note: If this conditions field is left empty, an exception is thrown. We don't want to consider 'empty conditions' to be a match-all case. Connector developers can determine and document what a match-all case constraint would be.", "location": "query", "type": "string"}, "entityType": {"description": "Required. Resource name of the Entity Type. Format: projects/{project}/locations/{location}/connections/{connection}/entityTypes/{type}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connections/[^/]+/entityTypes/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+entityType}/entities:deleteEntitiesWithConditions", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a single entity row matching the entity type and entity id specified in the request.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}/entityTypes/{entityTypesId}/entities/{entitiesId}", "httpMethod": "GET", "id": "connectors.projects.locations.connections.entityTypes.entities.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the Entity Type. Format: projects/{project}/locations/{location}/connections/{connection}/entityTypes/{type}/entities/{id}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connections/[^/]+/entityTypes/[^/]+/entities/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+name}", "response": {"$ref": "Entity"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists entity rows of a particular entity type contained in the request. Note: 1. Currently, only max of one 'sort_by' column is supported. 2. If no 'sort_by' column is provided, the primary key of the table is used. If zero or more than one primary key is available, we default to the unpaginated list entities logic which only returns the first page. 3. The values of the 'sort_by' columns must uniquely identify an entity row, otherwise undefined behaviors may be observed during pagination. 4. Since transactions are not supported, any updates, inserts or deletes during pagination can lead to stale data being returned or other unexpected behaviors.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}/entityTypes/{entityTypesId}/entities", "httpMethod": "GET", "id": "connectors.projects.locations.connections.entityTypes.entities.list", "parameterOrder": ["parent"], "parameters": {"conditions": {"description": "Conditions to be used when listing entities. From a proto standpoint, There are no restrictions on what can be passed using this field. The connector documentation should have information about what format of filters/conditions are supported.", "location": "query", "type": "string"}, "pageSize": {"description": "Number of entity rows to return. Defaults page size = 25. Max page size = 200.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Page token value if available from a previous request.", "location": "query", "type": "string"}, "parent": {"description": "Required. Resource name of the Entity Type. Format: projects/{project}/locations/{location}/connections/{connection}/entityTypes/{type}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connections/[^/]+/entityTypes/[^/]+$", "required": true, "type": "string"}, "sortBy": {"description": "List of 'sort_by' columns to use when returning the results.", "location": "query", "repeated": true, "type": "string"}, "sortOrder": {"description": "List of 'sort_order' columns to use when returning the results.", "location": "query", "repeated": true, "type": "string"}}, "path": "v2/{+parent}/entities", "response": {"$ref": "ListEntitiesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an existing entity row matching the entity type and entity id specified in the request. The fields in the entity row that need to be modified are contained in the body of the request. All unspecified fields are left unchanged. The response message contains a `Entity` message object returned as a response by the external system.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}/entityTypes/{entityTypesId}/entities/{entitiesId}", "httpMethod": "PATCH", "id": "connectors.projects.locations.connections.entityTypes.entities.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Resource name of the Entity. Format: projects/{project}/locations/{location}/connections/{connection}/entityTypes/{type}/entities/{id}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connections/[^/]+/entityTypes/[^/]+/entities/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+name}", "request": {"$ref": "Entity"}, "response": {"$ref": "Entity"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateEntitiesWithConditions": {"description": "Updates entities based on conditions specified in the request and not on entity id.", "flatPath": "v2/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}/entityTypes/{entityTypesId}/entities:updateEntitiesWithConditions", "httpMethod": "POST", "id": "connectors.projects.locations.connections.entityTypes.entities.updateEntitiesWithConditions", "parameterOrder": ["entityType"], "parameters": {"conditions": {"description": "Required. Conditions to be used when updating entities. From a proto standpoint, There are no restrictions on what can be passed using this field. The connector documentation should have information about what format of filters/conditions are supported. Note: If this conditions field is left empty, an exception is thrown. We don't want to consider 'empty conditions' to be a match-all case. Connector developers can determine and document what a match-all case constraint would be.", "location": "query", "type": "string"}, "entityType": {"description": "Required. Resource name of the Entity Type. Format: projects/{project}/locations/{location}/connections/{connection}/entityTypes/{type}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connections/[^/]+/entityTypes/[^/]+$", "required": true, "type": "string"}}, "path": "v2/{+entityType}/entities:updateEntitiesWithConditions", "request": {"$ref": "Entity"}, "response": {"$ref": "UpdateEntitiesWithConditionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}}}}}, "revision": "20250521", "rootUrl": "https://connectors.googleapis.com/", "schemas": {"AccessCredentials": {"description": "AccessCredentials includes the OAuth access token, and the other fields returned along with it.", "id": "AccessCredentials", "properties": {"accessToken": {"description": "OAuth access token.", "type": "string"}, "expiresIn": {"description": "Duration till the access token expires.", "format": "google-duration", "type": "string"}, "refreshToken": {"description": "OAuth refresh token.", "type": "string"}}, "type": "object"}, "Action": {"description": "Action message contains metadata information about a single action present in the external system.", "id": "Action", "properties": {"description": {"description": "Brief Description of action", "type": "string"}, "displayName": {"description": "Display Name of action to be shown on client side", "type": "string"}, "inputJsonSchema": {"$ref": "JsonSchema", "description": "JsonSchema representation of this actions's input schema"}, "inputParameters": {"description": "List containing input parameter metadata.", "items": {"$ref": "InputParameter"}, "type": "array"}, "name": {"description": "Name of the action.", "type": "string"}, "resultJsonSchema": {"$ref": "JsonSchema", "description": "JsonSchema representation of this actions's result schema"}, "resultMetadata": {"description": "List containing the metadata of result fields.", "items": {"$ref": "ResultMetadata"}, "type": "array"}}, "type": "object"}, "AuthCodeData": {"description": "AuthCodeData contains the data the runtime plane will give the connector backend in exchange for access and refresh tokens.", "id": "AuthCodeData", "properties": {"authCode": {"description": "OAuth authorization code.", "type": "string"}, "pkceVerifier": {"description": "OAuth PKCE verifier, needed if PKCE is enabled for this particular connection.", "type": "string"}, "redirectUri": {"description": "OAuth redirect URI passed in during the auth code flow, required by some OAuth backends.", "type": "string"}, "scopes": {"description": "Scopes the connection will request when the user performs the auth code flow.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "CheckReadinessResponse": {"description": "Response containing status of the connector for readiness prober.", "id": "CheckReadinessResponse", "properties": {"status": {"type": "string"}}, "type": "object"}, "CheckStatusResponse": {"description": "The status of the connector.", "id": "CheckStatusResponse", "properties": {"description": {"description": "When the connector is not in ACTIVE state, the description must be populated to specify the reason why it's not in ACTIVE state.", "type": "string"}, "state": {"description": "State of the connector.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "ERROR", "AUTH_ERROR"], "enumDescriptions": ["State unspecified.", "The connector is active and ready to process runtime requests. This can also mean that from the connector's perspective, the connector is not in an error state and should be able to process runtime requests successfully.", "The connector is in an error state and cannot process runtime requests. An example reason would be that the connection container has some network issues that prevent outbound requests from being sent.", "This is a more specific error state that the developers can opt to use when the connector is facing auth-related errors caused by auth configuration not present, invalid auth credentials, etc."], "type": "string"}}, "type": "object"}, "DailyCycle": {"description": "Time window specified for daily operations.", "id": "DailyCycle", "properties": {"duration": {"description": "Output only. Duration of the time window, set by service producer.", "format": "google-duration", "type": "string"}, "startTime": {"$ref": "TimeOfDay", "description": "Time within the day to start the operations."}}, "type": "object"}, "Date": {"description": "Represents a whole or partial calendar date, such as a birthday. The time of day and time zone are either specified elsewhere or are insignificant. The date is relative to the Gregorian Calendar. This can represent one of the following: * A full date, with non-zero year, month, and day values. * A month and day, with a zero year (for example, an anniversary). * A year on its own, with a zero month and a zero day. * A year and month, with a zero day (for example, a credit card expiration date). Related types: * google.type.TimeOfDay * google.type.DateTime * google.protobuf.Timestamp", "id": "Date", "properties": {"day": {"description": "Day of a month. Must be from 1 to 31 and valid for the year and month, or 0 to specify a year by itself or a year and month where the day isn't significant.", "format": "int32", "type": "integer"}, "month": {"description": "Month of a year. Must be from 1 to 12, or 0 to specify a year without a month and day.", "format": "int32", "type": "integer"}, "year": {"description": "Year of the date. Must be from 1 to 9999, or 0 to specify a date without a year.", "format": "int32", "type": "integer"}}, "type": "object"}, "DenyMaintenancePeriod": {"description": "DenyMaintenancePeriod definition. Maintenance is forbidden within the deny period. The start_date must be less than the end_date.", "id": "DenyMaintenancePeriod", "properties": {"endDate": {"$ref": "Date", "description": "Deny period end date. This can be: * A full date, with non-zero year, month and day values. * A month and day value, with a zero year. Allows recurring deny periods each year. Date matching this period will have to be before the end."}, "startDate": {"$ref": "Date", "description": "Deny period start date. This can be: * A full date, with non-zero year, month and day values. * A month and day value, with a zero year. Allows recurring deny periods each year. Date matching this period will have to be the same or after the start."}, "time": {"$ref": "TimeOfDay", "description": "Time in UTC when the Blackout period starts on start_date and ends on end_date. This can be: * Full time. * All zeros for 00:00:00 UTC"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "Entity": {"description": "'Entity row'/ 'Entity' refers to a single row of an entity type.", "id": "Entity", "properties": {"fields": {"additionalProperties": {"type": "any"}, "description": "Fields of the entity. The key is name of the field and the value contains the applicable `google.protobuf.Value` entry for this field.", "type": "object"}, "name": {"description": "Output only. Resource name of the Entity. Format: projects/{project}/locations/{location}/connections/{connection}/entityTypes/{type}/entities/{id}", "readOnly": true, "type": "string"}}, "type": "object"}, "EntityType": {"description": "EntityType message contains metadata information about a single entity type present in the external system.", "id": "EntityType", "properties": {"fields": {"description": "List containing metadata information about each field of the entity type.", "items": {"$ref": "Field"}, "type": "array"}, "jsonSchema": {"$ref": "JsonSchema", "description": "JsonSchema representation of this entity's schema"}, "name": {"description": "The name of the entity type.", "type": "string"}, "operations": {"items": {"enum": ["OPERATION_UNSPECIFIED", "LIST", "GET", "CREATE", "UPDATE", "DELETE"], "enumDescriptions": ["Operation unspecified.", "This operation means entity type supports LIST method.", "This operation means entity type supports GET method.", "This operation means entity type supports CREATE method.", "This operation means entity type supports UPDATE method.", "This operation means entity type supports DELETE method."], "type": "string"}, "type": "array"}}, "type": "object"}, "ExchangeAuthCodeRequest": {"description": "ExchangeAuthCodeRequest currently includes the auth code data.", "id": "ExchangeAuthCodeRequest", "properties": {"authCodeData": {"$ref": "AuthCodeData", "description": "Optional. AuthCodeData contains the data the runtime requires to exchange for access and refresh tokens. If the data is not provided, the runtime will read the data from the secret manager."}}, "type": "object"}, "ExchangeAuthCodeResponse": {"description": "ExchangeAuthCodeResponse includes the returned access token and its associated credentials.", "id": "ExchangeAuthCodeResponse", "properties": {"accessCredentials": {"$ref": "AccessCredentials"}}, "type": "object"}, "ExecuteActionRequest": {"description": "Request message for ActionService.ExecuteAction", "id": "ExecuteActionRequest", "properties": {"parameters": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Parameters for executing the action. The parameters can be key/value pairs or nested structs.", "type": "object"}}, "type": "object"}, "ExecuteActionResponse": {"description": "Response message for ActionService.ExecuteAction", "id": "ExecuteActionResponse", "properties": {"results": {"description": "In the case of successful invocation of the specified action, the results Struct contains values based on the response of the action invoked. 1. If the action execution produces any entities as a result, they are returned as an array of Structs with the 'key' being the field name and the 'value' being the value of that field in each result row. { 'results': [{'key': 'value'}, ...] }", "items": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "type": "object"}, "type": "array"}}, "type": "object"}, "ExecuteSqlQueryRequest": {"description": "An execute sql query request containing the query and the connection to execute it on.", "id": "ExecuteSqlQueryRequest", "properties": {"query": {"$ref": "Query", "description": "Required. SQL statement passed by clients like Integration Platform, the query is passed as-is to the driver used for interfacing with external systems."}}, "type": "object"}, "ExecuteSqlQueryResponse": {"description": "A response returned by the connection after executing the sql query.", "id": "ExecuteSqlQueryResponse", "properties": {"results": {"description": "In the case of successful execution of the query the response contains results returned by the external system. For example, the result rows of the query are contained in the 'results' Struct list - \"results\": [ { \"field1\": \"val1\", \"field2\": \"val2\",.. },.. ] Each Struct row can contain fields any type of like nested Structs or lists.", "items": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "type": "object"}, "type": "array"}}, "type": "object"}, "Field": {"description": "Message contains EntityType's Field metadata.", "id": "Field", "properties": {"additionalDetails": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "The following map contains fields that are not explicitly mentioned above,this give connectors the flexibility to add new metadata fields.", "type": "object"}, "dataType": {"description": "The data type of the Field.", "enum": ["DATA_TYPE_UNSPECIFIED", "INT", "SMALLINT", "DOUBLE", "DATE", "DATETIME", "TIME", "STRING", "LONG", "BOOLEAN", "DECIMAL", "UUID", "BLOB", "BIT", "TINYINT", "INTEGER", "BIGINT", "FLOAT", "REAL", "NUMERIC", "CHAR", "VARCHAR", "LONGVARCHAR", "TIMESTAMP", "NCHAR", "NVARCHAR", "LONGNVARCHAR", "NULL", "OTHER", "JAVA_OBJECT", "DISTINCT", "STRUCT", "ARRAY", "CLOB", "REF", "DATALINK", "ROWID", "BINARY", "VARBINARY", "LONGVARBINARY", "NCLOB", "SQLXML", "REF_CURSOR", "TIME_WITH_TIMEZONE", "TIMESTAMP_WITH_TIMEZONE"], "enumDeprecated": [false, true, false, false, false, true, false, true, true, false, false, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "enumDescriptions": ["Datatype unspecified.", "Deprecated Int type, use INTEGER type instead.", "Small int type.", "Double type.", "Date type.", "Deprecated Datetime type.", "Time type.", "Deprecated string type, use VARCHAR type instead.", "Deprecated Long type, use BIGINT type instead.", "Boolean type.", "Decimal type.", "Deprecated UUID type, use VARCHAR instead.", "Blob type.", "Bit type.", "Tiny int type.", "Integer type.", "Big int type.", "Float type.", "Real type.", "Numeric type.", "Char type.", "Varchar type.", "Long varchar type.", "Timestamp type.", "Nchar type.", "Nvarchar type.", "Long Nvarchar type.", "Null type.", "Other type.", "Java object type.", "Distinct type keyword.", "Struct type.", "Array type.", "Clob type.", "Ref type.", "Datalink type.", "Row ID type.", "Binary type.", "Varbinary type.", "Long Varbinary type.", "Nclob type.", "SQLXML type.", "Ref_cursor type.", "Time with timezone type.", "Timestamp with timezone type."], "type": "string"}, "defaultValue": {"description": "The following field specifies the default value of the Field provided by the external system if a value is not provided.", "type": "any"}, "description": {"description": "A brief description of the Field.", "type": "string"}, "jsonSchema": {"$ref": "JsonSchema", "description": "JsonSchema of the field, applicable only if field is of type `STRUCT`"}, "key": {"description": "The following boolean field specifies if the current Field acts as a primary key or id if the parent is of type entity.", "type": "boolean"}, "name": {"description": "Name of the Field.", "type": "string"}, "nullable": {"description": "Specifies whether a null value is allowed.", "type": "boolean"}, "reference": {"$ref": "Reference", "description": "Reference captures the association between two different entity types. Value links to the reference of another entity type."}}, "type": "object"}, "InputParameter": {"description": "Input Parameter message contains metadata about the parameters required for executing an Action.", "id": "InputParameter", "properties": {"additionalDetails": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "The following map contains fields that are not explicitly mentioned above,this give connectors the flexibility to add new metadata fields.", "type": "object"}, "dataType": {"description": "The data type of the Parameter", "enum": ["DATA_TYPE_UNSPECIFIED", "INT", "SMALLINT", "DOUBLE", "DATE", "DATETIME", "TIME", "STRING", "LONG", "BOOLEAN", "DECIMAL", "UUID", "BLOB", "BIT", "TINYINT", "INTEGER", "BIGINT", "FLOAT", "REAL", "NUMERIC", "CHAR", "VARCHAR", "LONGVARCHAR", "TIMESTAMP", "NCHAR", "NVARCHAR", "LONGNVARCHAR", "NULL", "OTHER", "JAVA_OBJECT", "DISTINCT", "STRUCT", "ARRAY", "CLOB", "REF", "DATALINK", "ROWID", "BINARY", "VARBINARY", "LONGVARBINARY", "NCLOB", "SQLXML", "REF_CURSOR", "TIME_WITH_TIMEZONE", "TIMESTAMP_WITH_TIMEZONE"], "enumDeprecated": [false, true, false, false, false, true, false, true, true, false, false, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "enumDescriptions": ["Datatype unspecified.", "Deprecated Int type, use INTEGER type instead.", "Small int type.", "Double type.", "Date type.", "Deprecated Datetime type.", "Time type.", "Deprecated string type, use VARCHAR type instead.", "Deprecated Long type, use BIGINT type instead.", "Boolean type.", "Decimal type.", "Deprecated UUID type, use VARCHAR instead.", "Blob type.", "Bit type.", "Tiny int type.", "Integer type.", "Big int type.", "Float type.", "Real type.", "Numeric type.", "Char type.", "Varchar type.", "Long varchar type.", "Timestamp type.", "Nchar type.", "Nvarchar type.", "Long Nvarchar type.", "Null type.", "Other type.", "Java object type.", "Distinct type keyword.", "Struct type.", "Array type.", "Clob type.", "Ref type.", "Datalink type.", "Row ID type.", "Binary type.", "Varbinary type.", "Long Varbinary type.", "Nclob type.", "SQLXML type.", "Ref_cursor type.", "Time with timezone type.", "Timestamp with timezone type."], "type": "string"}, "defaultValue": {"description": "The following field specifies the default value of the Parameter provided by the external system if a value is not provided.", "type": "any"}, "description": {"description": "A brief description of the Parameter.", "type": "string"}, "jsonSchema": {"$ref": "JsonSchema", "description": "JsonSchema of the parameter, applicable only if parameter is of type `STRUCT`"}, "name": {"description": "Name of the Parameter.", "type": "string"}, "nullable": {"description": "Specifies whether a null value is allowed.", "type": "boolean"}}, "type": "object"}, "Instance": {"description": "Instance represents the interface for SLM services to actuate the state of control plane resources. Example Instance in JSON, where consumer-project-number=123456, producer-project-id=cloud-sql: ```json Instance: { \"name\": \"projects/123456/locations/us-east1/instances/prod-instance\", \"create_time\": { \"seconds\": 1526406431, }, \"labels\": { \"env\": \"prod\", \"foo\": \"bar\" }, \"state\": READY, \"software_versions\": { \"software_update\": \"cloud-sql-09-28-2018\", }, \"maintenance_policy_names\": { \"UpdatePolicy\": \"projects/123456/locations/us-east1/maintenancePolicies/prod-update-policy\", } \"tenant_project_id\": \"cloud-sql-test-tenant\", \"producer_metadata\": { \"cloud-sql-tier\": \"basic\", \"cloud-sql-instance-size\": \"1G\", }, \"provisioned_resources\": [ { \"resource-type\": \"compute-instance\", \"resource-url\": \"https://www.googleapis.com/compute/v1/projects/cloud-sql/zones/us-east1-b/instances/vm-1\", } ], \"maintenance_schedules\": { \"csa_rollout\": { \"start_time\": { \"seconds\": 1526406431, }, \"end_time\": { \"seconds\": 1535406431, }, }, \"ncsa_rollout\": { \"start_time\": { \"seconds\": 1526406431, }, \"end_time\": { \"seconds\": 1535406431, }, } }, \"consumer_defined_name\": \"my-sql-instance1\", } ``` LINT.IfChange", "id": "Instance", "properties": {"consumerDefinedName": {"description": "consumer_defined_name is the name of the instance set by the service consumers. Generally this is different from the `name` field which reperesents the system-assigned id of the instance which the service consumers do not recognize. This is a required field for tenants onboarding to Maintenance Window notifications (go/slm-rollout-maintenance-policies#prerequisites).", "type": "string"}, "consumerProjectNumber": {"description": "Optional. The consumer_project_number associated with this Apigee instance. This field is added specifically to support Apigee integration with SLM Rollout and UMM. It represents the numerical project ID of the GCP project that consumes this Apigee instance. It is used for SLM rollout notifications and UMM integration, enabling proper mapping to customer projects and log delivery for Apigee instances. This field complements consumer_project_id and may be used for specific Apigee scenarios where the numerical ID is required.", "type": "string"}, "createTime": {"description": "Output only. Timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "instanceType": {"description": "Optional. The instance_type of this instance of format: projects/{project_number}/locations/{location_id}/instanceTypes/{instance_type_id}. Instance Type represents a high-level tier or SKU of the service that this instance belong to. When enabled(eg: Maintenance Rollout), Rollout uses 'instance_type' along with 'software_versions' to determine whether instance needs an update or not.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Resource labels to represent user provided metadata. Each label is a key-value pair, where both the key and the value are arbitrary strings provided by the user.", "type": "object"}, "maintenancePolicyNames": {"additionalProperties": {"type": "string"}, "description": "Optional. The MaintenancePolicies that have been attached to the instance. The key must be of the type name of the oneof policy name defined in MaintenancePolicy, and the referenced policy must define the same policy type. For details, please refer to go/mr-user-guide. Should not be set if maintenance_settings.maintenance_policies is set.", "type": "object"}, "maintenanceSchedules": {"additionalProperties": {"$ref": "MaintenanceSchedule"}, "description": "The MaintenanceSchedule contains the scheduling information of published maintenance schedule with same key as software_versions.", "type": "object"}, "maintenanceSettings": {"$ref": "MaintenanceSettings", "description": "Optional. The MaintenanceSettings associated with instance."}, "name": {"description": "Unique name of the resource. It uses the form: `projects/{project_number}/locations/{location_id}/instances/{instance_id}` Note: This name is passed, stored and logged across the rollout system. So use of consumer project_id or any other consumer PII in the name is strongly discouraged for wipeout (go/wipeout) compliance. See go/elysium/project_ids#storage-guidance for more details.", "type": "string"}, "notificationParameters": {"additionalProperties": {"$ref": "NotificationParameter"}, "description": "Optional. notification_parameter are information that service producers may like to include that is not relevant to Rollout. This parameter will only be passed to Gamma and Cloud Logging for notification/logging purpose.", "type": "object"}, "producerMetadata": {"additionalProperties": {"type": "string"}, "description": "Output only. Custom string attributes used primarily to expose producer-specific information in monitoring dashboards. See go/get-instance-metadata.", "readOnly": true, "type": "object"}, "provisionedResources": {"description": "Output only. The list of data plane resources provisioned for this instance, e.g. compute VMs. See go/get-instance-metadata.", "items": {"$ref": "ProvisionedResource"}, "readOnly": true, "type": "array"}, "slmInstanceTemplate": {"description": "Link to the SLM instance template. Only populated when updating SLM instances via SSA's Actuation service adaptor. Service producers with custom control plane (e.g. Cloud SQL) doesn't need to populate this field. Instead they should use software_versions.", "type": "string"}, "sloMetadata": {"$ref": "SloMetadata", "description": "Output only. SLO metadata for instance classification in the Standardized dataplane SLO platform. See go/cloud-ssa-standard-slo for feature description.", "readOnly": true}, "softwareVersions": {"additionalProperties": {"type": "string"}, "description": "Software versions that are used to deploy this instance. This can be mutated by rollout services.", "type": "object"}, "state": {"description": "Output only. Current lifecycle state of the resource (e.g. if it's being created or ready to use).", "enum": ["STATE_UNSPECIFIED", "CREATING", "READY", "UPDATING", "REPAIRING", "DELETING", "ERROR"], "enumDescriptions": ["Unspecified state.", "Instance is being created.", "Instance has been created and is ready to use.", "Instance is being updated.", "Instance is unheathy and under repair.", "Instance is being deleted.", "Instance encountered an error and is in indeterministic state."], "readOnly": true, "type": "string"}, "tenantProjectId": {"description": "Output only. ID of the associated GCP tenant project. See go/get-instance-metadata.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Timestamp when the resource was last modified.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "JsonSchema": {"description": "JsonSchema representation of schema metadata", "id": "JsonSchema", "properties": {"additionalDetails": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Additional details apart from standard json schema fields, this gives flexibility to store metadata about the schema", "type": "object"}, "default": {"description": "The default value of the field or object described by this schema.", "type": "any"}, "description": {"description": "A description of this schema.", "type": "string"}, "enum": {"description": "Possible values for an enumeration. This works in conjunction with `type` to represent types with a fixed set of legal values", "items": {"type": "any"}, "type": "array"}, "format": {"description": "Format of the value as per https://json-schema.org/understanding-json-schema/reference/string.html#format", "type": "string"}, "items": {"$ref": "JsonSchema", "description": "Schema that applies to array values, applicable only if this is of type `array`."}, "jdbcType": {"description": "JDBC datatype of the field.", "enum": ["DATA_TYPE_UNSPECIFIED", "INT", "SMALLINT", "DOUBLE", "DATE", "DATETIME", "TIME", "STRING", "LONG", "BOOLEAN", "DECIMAL", "UUID", "BLOB", "BIT", "TINYINT", "INTEGER", "BIGINT", "FLOAT", "REAL", "NUMERIC", "CHAR", "VARCHAR", "LONGVARCHAR", "TIMESTAMP", "NCHAR", "NVARCHAR", "LONGNVARCHAR", "NULL", "OTHER", "JAVA_OBJECT", "DISTINCT", "STRUCT", "ARRAY", "CLOB", "REF", "DATALINK", "ROWID", "BINARY", "VARBINARY", "LONGVARBINARY", "NCLOB", "SQLXML", "REF_CURSOR", "TIME_WITH_TIMEZONE", "TIMESTAMP_WITH_TIMEZONE"], "enumDeprecated": [false, true, false, false, false, true, false, true, true, false, false, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "enumDescriptions": ["Datatype unspecified.", "Deprecated Int type, use INTEGER type instead.", "Small int type.", "Double type.", "Date type.", "Deprecated Datetime type.", "Time type.", "Deprecated string type, use VARCHAR type instead.", "Deprecated Long type, use BIGINT type instead.", "Boolean type.", "Decimal type.", "Deprecated UUID type, use VARCHAR instead.", "Blob type.", "Bit type.", "Tiny int type.", "Integer type.", "Big int type.", "Float type.", "Real type.", "Numeric type.", "Char type.", "Varchar type.", "Long varchar type.", "Timestamp type.", "Nchar type.", "Nvarchar type.", "Long Nvarchar type.", "Null type.", "Other type.", "Java object type.", "Distinct type keyword.", "Struct type.", "Array type.", "Clob type.", "Ref type.", "Datalink type.", "Row ID type.", "Binary type.", "Varbinary type.", "Long Varbinary type.", "Nclob type.", "SQLXML type.", "Ref_cursor type.", "Time with timezone type.", "Timestamp with timezone type."], "type": "string"}, "properties": {"additionalProperties": {"$ref": "JsonSchema"}, "description": "The child schemas, applicable only if this is of type `object`. The key is the name of the property and the value is the json schema that describes that property", "type": "object"}, "required": {"description": "Whether this property is required.", "items": {"type": "string"}, "type": "array"}, "type": {"description": "JSON Schema Validation: A Vocabulary for Structural Validation of JSON", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListActionsResponse": {"description": "Response message for ActionService.ListActions", "id": "ListActionsResponse", "properties": {"actions": {"description": "List of action metadata.", "items": {"$ref": "Action"}, "type": "array"}, "nextPageToken": {"description": "Next page token if more actions available.", "type": "string"}, "unsupportedActionNames": {"description": "List of actions which contain unsupported Datatypes. Check datatype.proto for more information.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListEntitiesResponse": {"description": "Response message for EntityService.ListEntities", "id": "ListEntitiesResponse", "properties": {"entities": {"description": "List containing entity rows.", "items": {"$ref": "Entity"}, "type": "array"}, "nextPageToken": {"description": "Next page token if more records are available.", "type": "string"}}, "type": "object"}, "ListEntityTypesResponse": {"description": "Response message for EntityService.ListEntityTypes", "id": "ListEntityTypesResponse", "properties": {"nextPageToken": {"description": "Next page token if more entity types available.", "type": "string"}, "types": {"description": "List of metadata related to all entity types.", "items": {"$ref": "EntityType"}, "type": "array"}, "unsupportedTypeNames": {"description": "List of entity type names which contain unsupported Datatypes. Check datatype.proto for more information.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "MaintenancePolicy": {"description": "Defines policies to service maintenance events.", "id": "MaintenancePolicy", "properties": {"createTime": {"description": "Output only. The time when the resource was created.", "format": "google-datetime", "type": "string"}, "description": {"description": "Optional. Description of what this policy is for. Create/Update methods return INVALID_ARGUMENT if the length is greater than 512.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Resource labels to represent user provided metadata. Each label is a key-value pair, where both the key and the value are arbitrary strings provided by the user.", "type": "object"}, "name": {"description": "Required. MaintenancePolicy name using the form: `projects/{project_id}/locations/{location_id}/maintenancePolicies/{maintenance_policy_id}` where {project_id} refers to a GCP consumer project ID, {location_id} refers to a GCP region/zone, {maintenance_policy_id} must be 1-63 characters long and match the regular expression `[a-z0-9]([-a-z0-9]*[a-z0-9])?`.", "type": "string"}, "state": {"description": "Optional. The state of the policy.", "enum": ["STATE_UNSPECIFIED", "READY", "DELETING"], "enumDescriptions": ["Unspecified state.", "Resource is ready to be used.", "Resource is being deleted. It can no longer be attached to instances."], "type": "string"}, "updatePolicy": {"$ref": "UpdatePolicy", "description": "Maintenance policy applicable to instance update."}, "updateTime": {"description": "Output only. The time when the resource was updated.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "MaintenanceSchedule": {"description": "Maintenance schedule which is exposed to customer and potentially end user, indicating published upcoming future maintenance schedule", "id": "MaintenanceSchedule", "properties": {"canReschedule": {"deprecated": true, "description": "This field is deprecated, and will be always set to true since reschedule can happen multiple times now. This field should not be removed until all service producers remove this for their customers.", "type": "boolean"}, "endTime": {"description": "The scheduled end time for the maintenance.", "format": "google-datetime", "type": "string"}, "rolloutManagementPolicy": {"description": "The rollout management policy this maintenance schedule is associated with. When doing reschedule update request, the reschedule should be against this given policy.", "type": "string"}, "scheduleDeadlineTime": {"description": "schedule_deadline_time is the time deadline any schedule start time cannot go beyond, including reschedule. It's normally the initial schedule start time plus maintenance window length (1 day or 1 week). Maintenance cannot be scheduled to start beyond this deadline.", "format": "google-datetime", "type": "string"}, "startTime": {"description": "The scheduled start time for the maintenance.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "MaintenanceSettings": {"description": "Maintenance settings associated with instance. Allows service producers and end users to assign settings that controls maintenance on this instance.", "id": "MaintenanceSettings", "properties": {"exclude": {"description": "Optional. Exclude instance from maintenance. When true, rollout service will not attempt maintenance on the instance. Rollout service will include the instance in reported rollout progress as not attempted.", "type": "boolean"}, "isRollback": {"description": "Optional. If the update call is triggered from rollback, set the value as true.", "type": "boolean"}, "maintenancePolicies": {"additionalProperties": {"$ref": "MaintenancePolicy"}, "description": "Optional. The MaintenancePolicies that have been attached to the instance. The key must be of the type name of the oneof policy name defined in MaintenancePolicy, and the embedded policy must define the same policy type. For details, please refer to go/mr-user-guide. Should not be set if maintenance_policy_names is set. If only the name is needed, then only populate MaintenancePolicy.name.", "type": "object"}}, "type": "object"}, "MaintenanceWindow": {"description": "MaintenanceWindow definition.", "id": "MaintenanceWindow", "properties": {"dailyCycle": {"$ref": "DailyCycle", "description": "Daily cycle."}, "weeklyCycle": {"$ref": "WeeklyCycle", "description": "Weekly cycle."}}, "type": "object"}, "NodeSloMetadata": {"description": "Node information for custom per-node SLO implementations. SSA does not support per-node SLO, but producers can populate per-node information in SloMetadata for custom precomputations. SSA Eligibility Exporter will emit per-node metric based on this information.", "id": "NodeSloMetadata", "properties": {"location": {"description": "The location of the node, if different from instance location.", "type": "string"}, "nodeId": {"description": "The id of the node. This should be equal to SaasInstanceNode.node_id.", "type": "string"}, "perSliEligibility": {"$ref": "PerSliSloEligibility", "description": "If present, this will override eligibility for the node coming from instance or exclusions for specified SLIs."}}, "type": "object"}, "NotificationParameter": {"description": "Contains notification related data.", "id": "NotificationParameter", "properties": {"values": {"description": "Optional. Array of string values. e.g. instance's replica information.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "PerSliSloEligibility": {"description": "PerSliSloEligibility is a mapping from an SLI name to eligibility.", "id": "PerSliSloEligibility", "properties": {"eligibilities": {"additionalProperties": {"$ref": "SloEligibility"}, "description": "An entry in the eligibilities map specifies an eligibility for a particular SLI for the given instance. The SLI key in the name must be a valid SLI name specified in the Eligibility Exporter binary flags otherwise an error will be emitted by Eligibility Exporter and the oncaller will be alerted. If an SLI has been defined in the binary flags but the eligibilities map does not contain it, the corresponding SLI time series will not be emitted by the Eligibility Exporter. This ensures a smooth rollout and compatibility between the data produced by different versions of the Eligibility Exporters. If eligibilities map contains a key for an SLI which has not been declared in the binary flags, there will be an error message emitted in the Eligibility Exporter log and the metric for the SLI in question will not be emitted.", "type": "object"}}, "type": "object"}, "ProvisionedResource": {"description": "Describes provisioned dataplane resources.", "id": "ProvisionedResource", "properties": {"resourceType": {"description": "Type of the resource. This can be either a GCP resource or a custom one (e.g. another cloud provider's VM). For GCP compute resources use singular form of the names listed in GCP compute API documentation (https://cloud.google.com/compute/docs/reference/rest/v1/), prefixed with 'compute-', for example: 'compute-instance', 'compute-disk', 'compute-autoscaler'.", "type": "string"}, "resourceUrl": {"description": "URL identifying the resource, e.g. \"https://www.googleapis.com/compute/v1/projects/...)\".", "type": "string"}}, "type": "object"}, "Query": {"description": "A wrapper around the SQL query statement. This is needed so that the JSON representation of ExecuteSqlQueryRequest has the following format: `{\"query\":\"select *\"}`.", "id": "Query", "properties": {"maxRows": {"description": "Sets the limit for the maximum number of rows returned after the query execution.", "format": "int64", "type": "string"}, "query": {"description": "Required. Sql query to execute.", "type": "string"}, "queryParameters": {"description": "In the struct, the value corresponds to the value of query parameter and date type corresponds to the date type of the query parameter.", "items": {"$ref": "QueryParameter"}, "type": "array"}, "timeout": {"description": "Sets the number of seconds the driver will wait for a query to execute.", "format": "int64", "type": "string"}}, "type": "object"}, "QueryParameter": {"description": "Query parameter definition", "id": "QueryParameter", "properties": {"dataType": {"enum": ["DATA_TYPE_UNSPECIFIED", "INT", "SMALLINT", "DOUBLE", "DATE", "DATETIME", "TIME", "STRING", "LONG", "BOOLEAN", "DECIMAL", "UUID", "BLOB", "BIT", "TINYINT", "INTEGER", "BIGINT", "FLOAT", "REAL", "NUMERIC", "CHAR", "VARCHAR", "LONGVARCHAR", "TIMESTAMP", "NCHAR", "NVARCHAR", "LONGNVARCHAR", "NULL", "OTHER", "JAVA_OBJECT", "DISTINCT", "STRUCT", "ARRAY", "CLOB", "REF", "DATALINK", "ROWID", "BINARY", "VARBINARY", "LONGVARBINARY", "NCLOB", "SQLXML", "REF_CURSOR", "TIME_WITH_TIMEZONE", "TIMESTAMP_WITH_TIMEZONE"], "enumDeprecated": [false, true, false, false, false, true, false, true, true, false, false, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "enumDescriptions": ["Datatype unspecified.", "Deprecated Int type, use INTEGER type instead.", "Small int type.", "Double type.", "Date type.", "Deprecated Datetime type.", "Time type.", "Deprecated string type, use VARCHAR type instead.", "Deprecated Long type, use BIGINT type instead.", "Boolean type.", "Decimal type.", "Deprecated UUID type, use VARCHAR instead.", "Blob type.", "Bit type.", "Tiny int type.", "Integer type.", "Big int type.", "Float type.", "Real type.", "Numeric type.", "Char type.", "Varchar type.", "Long varchar type.", "Timestamp type.", "Nchar type.", "Nvarchar type.", "Long Nvarchar type.", "Null type.", "Other type.", "Java object type.", "Distinct type keyword.", "Struct type.", "Array type.", "Clob type.", "Ref type.", "Datalink type.", "Row ID type.", "Binary type.", "Varbinary type.", "Long Varbinary type.", "Nclob type.", "SQLXML type.", "Ref_cursor type.", "Time with timezone type.", "Timestamp with timezone type."], "type": "string"}, "value": {"type": "any"}}, "type": "object"}, "Reference": {"id": "Reference", "properties": {"name": {"description": "Name of the reference field.", "type": "string"}, "type": {"description": "Name of reference entity type.", "type": "string"}}, "type": "object"}, "RefreshAccessTokenRequest": {"description": "RefreshAccessTokenRequest includes the refresh token.", "id": "RefreshAccessTokenRequest", "properties": {"refreshToken": {"description": "Optional. Refresh Token String. If the Refresh Token is not provided, the runtime will read the data from the secret manager.", "type": "string"}}, "type": "object"}, "RefreshAccessTokenResponse": {"description": "RefreshAccessTokenResponse includes the returned access token and its associated credentials.", "id": "RefreshAccessTokenResponse", "properties": {"accessCredentials": {"$ref": "AccessCredentials"}}, "type": "object"}, "ResultMetadata": {"description": "Result Metadata message contains metadata about the result returned after executing an Action.", "id": "ResultMetadata", "properties": {"dataType": {"description": "The data type of the metadata field", "enum": ["DATA_TYPE_UNSPECIFIED", "INT", "SMALLINT", "DOUBLE", "DATE", "DATETIME", "TIME", "STRING", "LONG", "BOOLEAN", "DECIMAL", "UUID", "BLOB", "BIT", "TINYINT", "INTEGER", "BIGINT", "FLOAT", "REAL", "NUMERIC", "CHAR", "VARCHAR", "LONGVARCHAR", "TIMESTAMP", "NCHAR", "NVARCHAR", "LONGNVARCHAR", "NULL", "OTHER", "JAVA_OBJECT", "DISTINCT", "STRUCT", "ARRAY", "CLOB", "REF", "DATALINK", "ROWID", "BINARY", "VARBINARY", "LONGVARBINARY", "NCLOB", "SQLXML", "REF_CURSOR", "TIME_WITH_TIMEZONE", "TIMESTAMP_WITH_TIMEZONE"], "enumDeprecated": [false, true, false, false, false, true, false, true, true, false, false, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "enumDescriptions": ["Datatype unspecified.", "Deprecated Int type, use INTEGER type instead.", "Small int type.", "Double type.", "Date type.", "Deprecated Datetime type.", "Time type.", "Deprecated string type, use VARCHAR type instead.", "Deprecated Long type, use BIGINT type instead.", "Boolean type.", "Decimal type.", "Deprecated UUID type, use VARCHAR instead.", "Blob type.", "Bit type.", "Tiny int type.", "Integer type.", "Big int type.", "Float type.", "Real type.", "Numeric type.", "Char type.", "Varchar type.", "Long varchar type.", "Timestamp type.", "Nchar type.", "Nvarchar type.", "Long Nvarchar type.", "Null type.", "Other type.", "Java object type.", "Distinct type keyword.", "Struct type.", "Array type.", "Clob type.", "Ref type.", "Datalink type.", "Row ID type.", "Binary type.", "Varbinary type.", "Long Varbinary type.", "Nclob type.", "SQLXML type.", "Ref_cursor type.", "Time with timezone type.", "Timestamp with timezone type."], "type": "string"}, "defaultValue": {"description": "The following field specifies the default value of the Parameter provided by the external system if a value is not provided.", "type": "any"}, "description": {"description": "A brief description of the metadata field.", "type": "string"}, "jsonSchema": {"$ref": "JsonSchema", "description": "JsonSchema of the result, applicable only if parameter is of type `STRUCT`"}, "name": {"description": "Name of the metadata field.", "type": "string"}, "nullable": {"description": "Specifies whether a null value is allowed.", "type": "boolean"}}, "type": "object"}, "Schedule": {"description": "Configure the schedule.", "id": "Schedule", "properties": {"day": {"description": "Allows to define schedule that runs specified day of the week.", "enum": ["DAY_OF_WEEK_UNSPECIFIED", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"], "enumDescriptions": ["The day of the week is unspecified.", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"], "type": "string"}, "duration": {"description": "Output only. Duration of the time window, set by service producer.", "format": "google-duration", "type": "string"}, "startTime": {"$ref": "TimeOfDay", "description": "Time within the window to start the operations."}}, "type": "object"}, "SloEligibility": {"description": "SloEligibility is a tuple containing eligibility value: true if an instance is eligible for SLO calculation or false if it should be excluded from all SLO-related calculations along with a user-defined reason.", "id": "SloEligibility", "properties": {"eligible": {"description": "Whether an instance is eligible or ineligible.", "type": "boolean"}, "reason": {"description": "User-defined reason for the current value of instance eligibility. Usually, this can be directly mapped to the internal state. An empty reason is allowed.", "type": "string"}}, "type": "object"}, "SloMetadata": {"description": "SloMetadata contains resources required for proper SLO classification of the instance.", "id": "SloMetadata", "properties": {"nodes": {"description": "Optional. List of nodes. Some producers need to use per-node metadata to calculate SLO. This field allows such producers to publish per-node SLO meta data, which will be consumed by SSA Eligibility Exporter and published in the form of per node metric to Monarch.", "items": {"$ref": "NodeSloMetadata"}, "type": "array"}, "perSliEligibility": {"$ref": "PerSliSloEligibility", "description": "Optional. Multiple per-instance SLI eligibilities which apply for individual SLIs."}, "tier": {"description": "Name of the SLO tier the Instance belongs to. This name will be expected to match the tiers specified in the service SLO configuration. Field is mandatory and must not be empty.", "type": "string"}}, "type": "object"}, "TimeOfDay": {"description": "Represents a time of day. The date and time zone are either not significant or are specified elsewhere. An API may choose to allow leap seconds. Related types are google.type.Date and `google.protobuf.Timestamp`.", "id": "TimeOfDay", "properties": {"hours": {"description": "Hours of a day in 24 hour format. Must be greater than or equal to 0 and typically must be less than or equal to 23. An API may choose to allow the value \"24:00:00\" for scenarios like business closing time.", "format": "int32", "type": "integer"}, "minutes": {"description": "Minutes of an hour. Must be greater than or equal to 0 and less than or equal to 59.", "format": "int32", "type": "integer"}, "nanos": {"description": "Fractions of seconds, in nanoseconds. Must be greater than or equal to 0 and less than or equal to 999,999,999.", "format": "int32", "type": "integer"}, "seconds": {"description": "Seconds of a minute. Must be greater than or equal to 0 and typically must be less than or equal to 59. An API may allow the value 60 if it allows leap-seconds.", "format": "int32", "type": "integer"}}, "type": "object"}, "UpdateEntitiesWithConditionsResponse": {"description": "Response message for EntityService.UpdateEntitiesWithConditions", "id": "UpdateEntitiesWithConditionsResponse", "properties": {"response": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Response returned by the external system.", "type": "object"}}, "type": "object"}, "UpdatePolicy": {"description": "Maintenance policy applicable to instance updates.", "id": "UpdatePolicy", "properties": {"channel": {"description": "Optional. Relative scheduling channel applied to resource.", "enum": ["UPDATE_CHANNEL_UNSPECIFIED", "EARLIER", "LATER", "WEEK1", "WEEK2", "WEEK5"], "enumDescriptions": ["Unspecified channel.", "Early channel within a customer project.", "Later channel within a customer project.", "! ! The follow channels can ONLY be used if you adopt the new MW system! ! ! NOTE: all WEEK channels are assumed to be under a weekly window. ! There is currently no dedicated channel definitions for Daily windows. ! If you use Daily window, the system will assume a 1d (24Hours) advanced ! notification period b/w EARLY and LATER. ! We may consider support more flexible daily channel specifications in ! the future. WEEK1 == EARLIER with minimum 7d advanced notification. {7d, 14d} The system will treat them equally and will use WEEK1 whenever it can. New customers are encouraged to use this channel annotation.", "WEEK2 == LATER with minimum 14d advanced notification {14d, 21d}.", "WEEK5 == 40d support. minimum 35d advanced notification {35d, 42d}."], "type": "string"}, "denyMaintenancePeriods": {"description": "Deny Maintenance Period that is applied to resource to indicate when maintenance is forbidden. The protocol supports zero-to-many such periods, but the current SLM Rollout implementation only supports zero-to-one.", "items": {"$ref": "DenyMaintenancePeriod"}, "type": "array"}, "window": {"$ref": "MaintenanceWindow", "description": "Optional. Maintenance window that is applied to resources covered by this policy."}}, "type": "object"}, "WeeklyCycle": {"description": "Time window specified for weekly operations.", "id": "WeeklyCycle", "properties": {"schedule": {"description": "User can specify multiple windows in a week. Minimum of 1 window.", "items": {"$ref": "Schedule"}, "type": "array"}}, "type": "object"}}, "servicePath": "", "title": "Connectors API", "version": "v2", "version_module": true}