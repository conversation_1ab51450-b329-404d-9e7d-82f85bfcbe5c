# Comparativo Detalhado dos Modelos LLM da Google

## Tabela Comparativa Completa dos Modelos Google AI

| Modelo | Cota Free | Contexto (Tokens) | Entrada | Saída | Thinking | Imagem | Áudio | Vídeo | RPM Free | RPD Free | TPM Free | Preço Input ($/1M) | Preço Output ($/1M) | Observações |
|--------|-----------|-------------------|---------|-------|----------|--------|-------|-------|----------|----------|----------|-------------------|---------------------|-------------|
| **Gemini 2.5 Pro** | ❌ | 1,048,576 | <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, PDF | Texto | ✅ | ✅ | ✅ | ✅ | -- | -- | -- | $1.25-2.50 | $10.00-15.00 | <PERSON>o mais avançado, melhor para raciocínio complexo |
| **Gemini 2.5 Flash** | ✅ | 1,048,576 | Texto, Imagem, Áudio, Vídeo | Texto | ✅ | ✅ | ✅ | ✅ | 10 | 250 | 250,000 | $0.30-1.00 | $2.50 | Melhor custo-benefício, thinking adaptativo |
| **Gemini 2.5 Flash-Lite** | ✅ | 1,000,000 | Texto, Imagem, Áudio, Vídeo | Texto | ✅ | ✅ | ✅ | ✅ | 15 | 1,000 | 250,000 | $0.10-0.50 | $0.40 | Mais econômico, baixa latência |
| **Gemini 2.5 Flash TTS** | ✅ | 8,000 | Texto | Áudio | ❌ | ❌ | ✅ | ❌ | 3 | 15 | 10,000 | $0.50 | $10.00 | Conversão texto para fala |
| **Gemini 2.5 Pro TTS** | ❌ | 8,000 | Texto | Áudio | ❌ | ❌ | ✅ | ❌ | -- | -- | -- | $1.00 | $20.00 | TTS mais poderoso |
| **Gemini 2.0 Flash** | ✅ | 1,048,576 | Texto, Imagem, Áudio, Vídeo | Texto | Experimental | ✅ | ✅ | ✅ | 15 | 200 | 1,000,000 | $0.10-0.70 | $0.40 | Nova geração, ferramentas nativas |
| **Gemini 2.0 Flash-Lite** | ✅ | 1,048,576 | Texto, Imagem, Áudio, Vídeo | Texto | ❌ | ✅ | ✅ | ✅ | 30 | 200 | 1,000,000 | $0.075 | $0.30 | Versão econômica do 2.0 |
| **Gemini 1.5 Flash** | ✅ | 1,048,576 | Texto, Imagem, Áudio, Vídeo | Texto | ❌ | ✅ | ✅ | ✅ | 15 | 50 | 250,000 | $0.075-0.15 | $0.30-0.60 | Modelo versátil, depreciado |
| **Gemini 1.5 Flash-8B** | ✅ | 1,048,576 | Texto, Imagem, Áudio, Vídeo | Texto | ❌ | ✅ | ✅ | ✅ | 15 | 50 | 250,000 | $0.0375-0.075 | $0.15-0.30 | Modelo menor, depreciado |
| **Gemini 1.5 Pro** | ❌ | 2,097,152 | Texto, Imagem, Áudio, Vídeo | Texto | ❌ | ✅ | ✅ | ✅ | -- | -- | -- | $1.25-2.50 | $5.00-10.00 | Maior contexto, depreciado |
| **Gemma 3** | ✅ | N/A | Texto | Texto | ❌ | ❌ | ❌ | ❌ | 30 | 14,400 | 15,000 | Grátis | Grátis | Modelo open source |
| **Gemma 3n** | ✅ | N/A | Texto | Texto | ❌ | ❌ | ❌ | ❌ | 30 | 14,400 | 15,000 | Grátis | Grátis | Otimizado para dispositivos |
| **Imagen 4** | ❌ | 480 | Texto | Imagem | ❌ | ❌ | ❌ | ❌ | -- | -- | -- | -- | $0.04-0.06/img | Geração de imagens avançada |
| **Imagen 3** | ❌ | N/A | Texto | Imagem | ❌ | ❌ | ❌ | ❌ | -- | -- | -- | -- | $0.03/img | Geração de imagens |
| **Veo 2** | ❌ | N/A | Texto, Imagem | Vídeo | ❌ | ❌ | ❌ | ❌ | -- | -- | -- | -- | $0.35/seg | Geração de vídeos |
| **Text Embedding 004** | ✅ | 2,048 | Texto | Embeddings | ❌ | ❌ | ❌ | ❌ | 1,500 | -- | -- | Grátis | Grátis | Embeddings de texto |
| **Gemini Embedding Exp** | ✅ | 8,192 | Texto | Embeddings | ❌ | ❌ | ❌ | ❌ | 5 | 100 | -- | Grátis | Grátis | Embeddings experimentais |

## Modelos Live API (Tempo Real)

| Modelo | Cota Free | Sessões Simultâneas | Entrada | Saída | RPM Free | TPM Free | Preço Input ($/1M) | Preço Output ($/1M) |
|--------|-----------|---------------------|---------|-------|----------|----------|-------------------|---------------------|
| **Gemini 2.5 Flash Live** | ✅ | 3 | Áudio, Vídeo, Texto | Texto, Áudio | -- | 1,000,000 | $0.50-3.00 | $2.00-12.00 |
| **Gemini 2.0 Flash Live** | ✅ | 3 | Áudio, Vídeo, Texto | Texto, Áudio | -- | 1,000,000 | $0.35-2.10 | $1.50-8.50 |

## Níveis de Uso (Tiers)

### Tier Gratuito
- **Requisitos**: Usuários em países elegíveis
- **Limitações**: RPM e RPD reduzidos, alguns modelos indisponíveis

### Tier 1 (Pago)
- **Requisitos**: Conta de cobrança vinculada ao projeto
- **Benefícios**: Limites aumentados, acesso a todos os modelos

### Tier 2 (Pago Avançado)
- **Requisitos**: >$250 gastos + 30 dias de pagamento
- **Benefícios**: Limites muito maiores

### Tier 3 (Empresarial)
- **Requisitos**: >$1000 gastos + 30 dias de pagamento
- **Benefícios**: Limites máximos disponíveis

## Funcionalidades Especiais

### Thinking Mode (Modo Pensamento)
- **Disponível em**: Gemini 2.5 Pro, 2.5 Flash, 2.5 Flash-Lite
- **Função**: Modelo "pensa" antes de responder, melhorando qualidade

### Context Caching (Cache de Contexto)
- **Disponível em**: Maioria dos modelos 2.5 e 2.0
- **Função**: Reduz custos para contextos longos reutilizados

### Function Calling (Chamada de Funções)
- **Disponível em**: Modelos principais (exceto TTS e embeddings)
- **Função**: Permite integração com ferramentas externas

### Code Execution (Execução de Código)
- **Disponível em**: Modelos principais
- **Função**: Executa código Python diretamente

## Observações Importantes

1. **Modelos 1.5**: Marcados como depreciados, use versões 2.x
2. **Preços variáveis**: Dependem do tamanho da requisição
3. **Limites dinâmicos**: Podem variar baseado na capacidade
4. **Modelos experimentais**: Limites mais restritivos
5. **Google Search**: Grounding disponível com custos adicionais

*Última atualização: Janeiro 2025*
