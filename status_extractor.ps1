# Script para verificar status do MT4 Extractor
# Autor: Sistema de Extração MT4
# Data: 2025-01-26

Write-Host "📊 Status do MT4 Extractor" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

$pidFile = ".\extractor.pid"

# Verificar arquivo PID
if (Test-Path $pidFile) {
    try {
        $pid = Get-Content $pidFile -ErrorAction Stop
        Write-Host "📁 Arquivo PID encontrado: $pid" -ForegroundColor Green
        
        $process = Get-Process -Id $pid -ErrorAction SilentlyContinue
        if ($process) {
            Write-Host "✅ Status: RODANDO" -ForegroundColor Green
            Write-Host "🔍 PID: $pid" -ForegroundColor White
            Write-Host "⏰ Iniciado: $($process.StartTime)" -ForegroundColor White
            Write-Host "💾 Uso de Memória: $([math]::Round($process.WorkingSet64/1MB, 2)) MB" -ForegroundColor White
        } else {
            Write-Host "❌ Status: PARADO (PID inválido)" -ForegroundColor Red
            Remove-Item $pidFile -Force
            Write-Host "🗑️  Arquivo PID removido (processo não existe)" -ForegroundColor Gray
        }
    } catch {
        Write-Host "❌ Erro ao ler arquivo PID: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "📁 Arquivo PID não encontrado" -ForegroundColor Yellow
    
    # Procurar processos Python com extractor
    $extractorProcesses = Get-Process -Name "python" -ErrorAction SilentlyContinue | Where-Object {
        try {
            $commandLine = (Get-WmiObject Win32_Process -Filter "ProcessId = $($_.Id)").CommandLine
            return $commandLine -like "*extractor.py*"
        } catch {
            return $false
        }
    }
    
    if ($extractorProcesses) {
        Write-Host "⚠️  Status: RODANDO (sem controle PID)" -ForegroundColor Yellow
        foreach ($proc in $extractorProcesses) {
            Write-Host "🔍 PID encontrado: $($proc.Id)" -ForegroundColor White
            Write-Host "⏰ Iniciado: $($proc.StartTime)" -ForegroundColor White
        }
    } else {
        Write-Host "❌ Status: PARADO" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "📈 Verificação de Arquivos CSV:" -ForegroundColor Yellow

$csvFiles = @("D1_1.csv", "H4_1.csv", "H4_2.csv", "H4_3.csv")
foreach ($file in $csvFiles) {
    if (Test-Path $file) {
        $fileInfo = Get-Item $file
        $lineCount = (Get-Content $file | Measure-Object -Line).Lines - 1  # -1 para excluir cabeçalho
        Write-Host "  ✅ $file - $lineCount registros - Modificado: $($fileInfo.LastWriteTime)" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $file - Não encontrado" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "🖥️  Verificação do MT4:" -ForegroundColor Yellow

$mt4Process = Get-Process -Name "terminal" -ErrorAction SilentlyContinue
if ($mt4Process) {
    Write-Host "  ✅ MT4 rodando (PID: $($mt4Process.Id))" -ForegroundColor Green
} else {
    Write-Host "  ❌ MT4 não detectado" -ForegroundColor Red
}

Write-Host ""
Write-Host "⏰ Próximas Execuções (horários sincronizados):" -ForegroundColor Yellow

$now = Get-Date
$currentMinute = $now.Minute
$nextMinutes = @(0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55)

$nextExecution = $null
foreach ($minute in $nextMinutes) {
    if ($minute -gt $currentMinute) {
        $nextExecution = $now.Date.AddHours($now.Hour).AddMinutes($minute)
        break
    }
}

if (-not $nextExecution) {
    # Próxima hora
    $nextExecution = $now.Date.AddHours($now.Hour + 1).AddMinutes(0)
}

Write-Host "  🕐 Próxima: $($nextExecution.ToString('HH:mm:ss'))" -ForegroundColor White
Write-Host "  ⏳ Em: $([math]::Round(($nextExecution - $now).TotalMinutes, 1)) minutos" -ForegroundColor White

Write-Host ""
Write-Host "=================================" -ForegroundColor Cyan

Write-Host ""
Write-Host "Pressione qualquer tecla para sair..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
