import win32gui, win32ui, win32con, win32process
from PIL import Image
import numpy as np
import google.generativeai as genai
import json
import time
import io
import logging
from datetime import datetime
import psutil
import ctypes
from ctypes import wintypes
import csv
import os

# Corrigir PrintWindow ausente no win32gui
if not hasattr(win32gui, 'PrintWindow'):
    user32 = ctypes.windll.user32
    PrintWindow = user32.PrintWindow
    PrintWindow.argtypes = [wintypes.HWND, wintypes.HDC, wintypes.UINT]
    PrintWindow.restype = wintypes.BOOL
    win32gui.PrintWindow = PrintWindow
    print("✅ PrintWindow adicionado ao win32gui via ctypes!")

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MT4TableExtractor:
    def __init__(self, gemini_api_key):
        """Inicializar extrator com API key do Gemini"""
        genai.configure(api_key=gemini_api_key)
        self.model = genai.GenerativeModel('gemini-1.5-flash')
        self.mt4_windows = []
        
    def find_mt4_by_executable_path(self, exe_path):
        """Encontrar janelas MT4 pelo caminho do executável"""
        target_windows = []
        
        # Encontrar processos pelo caminho
        for proc in psutil.process_iter(['pid', 'name', 'exe']):
            try:
                if proc.info['exe'] and exe_path.lower() in proc.info['exe'].lower():
                    pid = proc.info['pid']
                    
                    # Encontrar janelas deste processo
                    def enum_handler(hwnd, windows):
                        if win32gui.IsWindowVisible(hwnd):
                            _, window_pid = win32process.GetWindowThreadProcessId(hwnd)
                            if window_pid == pid:
                                windows.append({
                                    'handle': hwnd,
                                    'title': win32gui.GetWindowText(hwnd),
                                    'rect': win32gui.GetWindowRect(hwnd),
                                    'pid': pid,
                                    'exe_path': proc.info['exe']
                                })
                    
                    win32gui.EnumWindows(enum_handler, target_windows)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        return target_windows
    
    def capture_window_content(self, window_handle):
        """
        Captura conteúdo da janela mesmo se estiver coberta por outras janelas
        """
        try:
            # Obter dimensões da janela
            left, top, right, bottom = win32gui.GetWindowRect(window_handle)
            width = right - left
            height = bottom - top
            
            # Criar contextos de dispositivo
            hwndDC = win32gui.GetWindowDC(window_handle)
            mfcDC = win32ui.CreateDCFromHandle(hwndDC)
            saveDC = mfcDC.CreateCompatibleDC()
            
            # Criar bitmap
            saveBitMap = win32ui.CreateBitmap()
            saveBitMap.CreateCompatibleBitmap(mfcDC, width, height)
            saveDC.SelectObject(saveBitMap)
            
            # CAPTURA DIRETA DA JANELA (funciona mesmo se coberta)
            # Flag 3 = PW_RENDERFULLCONTENT (captura conteúdo completo)
            result = win32gui.PrintWindow(window_handle, saveDC.GetSafeHdc(), 3)
            
            if result:
                # Converter para PIL Image
                bmpinfo = saveBitMap.GetInfo()
                bmpstr = saveBitMap.GetBitmapBits(True)
                im = Image.frombuffer('RGB', (bmpinfo['bmWidth'], bmpinfo['bmHeight']), 
                                    bmpstr, 'raw', 'BGRX', 0, 1)
            else:
                logger.error("PrintWindow falhou")
                im = None
            
            # Limpeza de recursos
            win32gui.DeleteObject(saveBitMap.GetHandle())
            saveDC.DeleteDC()
            mfcDC.DeleteDC()
            win32gui.ReleaseDC(window_handle, hwndDC)
            
            return im
            
        except Exception as e:
            logger.error(f"Erro ao capturar janela: {e}")
            return None
    
    def extract_table_data_with_gemini(self, image, window_title=""):
        """
        Extrai dados da tabela usando Gemini Vision
        """
        try:
            # Converter PIL Image para bytes
            img_byte_arr = io.BytesIO()
            image.save(img_byte_arr, format='PNG')
            img_byte_arr.seek(0)
            
            # Prompt específico para os 4 indicadores MT4
            prompt = f"""
            Analise esta imagem da janela MT4 "{window_title}" que contém 4 indicadores de força de moedas.

            Vejo 4 indicadores organizados em uma grade 2x2:
            - Superior esquerdo: D1 (1)
            - Superior direito: H4 (3)
            - Inferior esquerdo: H4 (1)
            - Inferior direito: H4 (2)

            Cada indicador mostra 8 moedas com seus valores numéricos abaixo.

            Extraia os dados de TODOS OS 4 INDICADORES mantendo a ordem EXATA das moedas como aparecem.

            Retorne em formato JSON:
            {{
                "timestamp": "{datetime.now().isoformat()}",
                "window": "{window_title}",
                "indicators": [
                    {{
                        "name": "D1 (1)",
                        "position": "top-left",
                        "currencies": [
                            {{"currency": "USD", "value": 7.9}},
                            {{"currency": "EUR", "value": 4.4}}
                        ]
                    }},
                    {{
                        "name": "H4 (3)",
                        "position": "top-right",
                        "currencies": [...]
                    }},
                    {{
                        "name": "H4 (1)",
                        "position": "bottom-left",
                        "currencies": [...]
                    }},
                    {{
                        "name": "H4 (2)",
                        "position": "bottom-right",
                        "currencies": [...]
                    }}
                ]
            }}

            CRÍTICO:
            - Mantenha a ordem EXATA das moedas como aparecem em cada indicador
            - Extraia os valores numéricos precisos (decimais)
            - Identifique corretamente qual indicador é qual (D1/H4 e números)
            - Se algum valor não estiver legível, use null
            """
            
            # Enviar para Gemini
            response = self.model.generate_content([prompt, image])
            
            # Tentar extrair JSON da resposta
            response_text = response.text.strip()
            
            # Limpar resposta se tiver markdown
            if response_text.startswith('```json'):
                response_text = response_text.replace('```json', '').replace('```', '').strip()
            elif response_text.startswith('```'):
                response_text = response_text.replace('```', '').strip()
            
            # Parsear JSON
            try:
                data = json.loads(response_text)
                return data
            except json.JSONDecodeError:
                logger.error(f"Erro ao parsear JSON: {response_text}")
                return None
                
        except Exception as e:
            logger.error(f"Erro ao processar com Gemini: {e}")
            return None

    def save_to_csv(self, data):
        """
        Salva dados dos indicadores em arquivos CSV separados
        """
        if not data or 'indicators' not in data:
            logger.warning("Nenhum dado de indicadores para salvar")
            return

        # Ordem fixa das moedas para o cabeçalho CSV
        currency_columns = ['USD', 'EUR', 'GBP', 'JPY', 'AUD', 'NZD', 'CAD', 'CHF']

        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        for indicator in data['indicators']:
            indicator_name = indicator['name']

            # Nome do arquivo CSV (remover caracteres especiais)
            filename = f"{indicator_name.replace(' ', '_').replace('(', '').replace(')', '')}.csv"

            # Criar dicionário para mapear moedas aos valores
            currency_values = {}
            for currency_data in indicator['currencies']:
                currency_values[currency_data['currency']] = currency_data['value']

            # Verificar se arquivo existe para decidir se escreve cabeçalho
            file_exists = os.path.exists(filename)

            # Escrever no CSV
            with open(filename, 'a', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)

                # Escrever cabeçalho se arquivo não existe
                if not file_exists:
                    header = ['timestamp'] + currency_columns
                    writer.writerow(header)
                    logger.info(f"Criado arquivo CSV: {filename}")

                # Preparar linha de dados
                row = [timestamp]
                for currency in currency_columns:
                    # Adicionar valor da moeda ou None se não encontrada
                    value = currency_values.get(currency, None)
                    row.append(value)

                writer.writerow(row)
                logger.info(f"Dados salvos em {filename}: {len([v for v in currency_values.values() if v is not None])} moedas")

    def extract_all_tables(self):
        """
        Extrai dados de todas as janelas MT4 encontradas
        """
        if not self.mt4_windows:
            logger.warning("Nenhuma janela MT4 encontrada. Use find_mt4_by_executable_path() primeiro.")
            return []
        
        all_data = []
        
        for i, window_info in enumerate(self.mt4_windows):
            logger.info(f"Processando janela {i+1}/{len(self.mt4_windows)}: {window_info['title']}")
            
            # Capturar imagem da janela inteira
            image = self.capture_window_content(window_info['handle'])
            
            if image:
                # Extrair dados com Gemini
                data = self.extract_table_data_with_gemini(image, window_info['title'])
                
                if data:
                    data['window_id'] = i + 1
                    all_data.append(data)
                    logger.info(f"Dados extraídos da janela {i+1}")

                    # Salvar em CSV
                    self.save_to_csv(data)
                else:
                    logger.warning(f"Falha ao extrair dados da janela {i+1}")
            else:
                logger.error(f"Falha ao capturar janela {i+1}")
        
        return all_data
    
    def run_continuous_extraction(self, interval_seconds=60, save_to_file=True):
        """
        Executa extração contínua a cada intervalo especificado
        """
        logger.info(f"Iniciando extração contínua a cada {interval_seconds} segundos")
        
        while True:
            try:
                timestamp = datetime.now()
                logger.info(f"=== Extração em {timestamp.strftime('%Y-%m-%d %H:%M:%S')} ===")
                
                # Extrair dados de todas as tabelas
                all_data = self.extract_all_tables()
                
                if all_data:
                    # Salvar em arquivo se solicitado
                    if save_to_file:
                        filename = f"mt4_data_{timestamp.strftime('%Y%m%d_%H%M%S')}.json"
                        with open(filename, 'w', encoding='utf-8') as f:
                            json.dump(all_data, f, indent=2, ensure_ascii=False)
                        logger.info(f"Dados salvos em {filename}")
                    
                    # Exibir resumo
                    for data in all_data:
                        if 'header_data' in data and data['header_data']:
                            logger.info(f"Janela {data['window_id']}: {len(data['header_data'])} moedas extraídas")
                
                # Aguardar próximo ciclo
                logger.info(f"Aguardando {interval_seconds} segundos...")
                time.sleep(interval_seconds)
                
            except KeyboardInterrupt:
                logger.info("Extração interrompida pelo usuário")
                break
            except Exception as e:
                logger.error(f"Erro durante extração: {e}")
                time.sleep(interval_seconds)

# Exemplo de uso
if __name__ == "__main__":
    # Configurar sua API key do Gemini
    GEMINI_API_KEY = "AIzaSyD2JyV7_xZCvju0LReFB6gLTh_N5kIy7xY"
    
    # Caminho do executável MT4
    exe_path = r"C:\Program Files (x86)\MetaTrader 4 EXNESS\terminal.exe"
    
    # Criar extrator
    extractor = MT4TableExtractor(GEMINI_API_KEY)
    
    # Encontrar janelas MT4 pelo executável
    windows = extractor.find_mt4_by_executable_path(exe_path)
    extractor.mt4_windows = windows
    
    if windows:
        print(f"Encontradas {len(windows)} janelas MT4:")
        for i, window in enumerate(windows):
            print(f"{i+1}. {window['title']}")
        
        # Executar extração única (teste)
        print("\n=== Teste de extração única ===")
        data = extractor.extract_all_tables()
        if data:
            print(json.dumps(data, indent=2, ensure_ascii=False))
        
        # Executar extração contínua (descomente para usar)
        # print("\n=== Iniciando extração contínua ===")
        # extractor.run_continuous_extraction(interval_seconds=60)
    else:
        print("Nenhuma janela MT4 encontrada!")
        print("Certifique-se de que o MT4 está aberto.")


