# Script para parar o MT4 Extractor
# Autor: Sistema de Extração MT4
# Data: 2025-01-26

Write-Host "🛑 Parando MT4 Extractor..." -ForegroundColor Red
Write-Host "=================================" -ForegroundColor Cyan

$pidFile = ".\extractor.pid"

# Verificar se existe arquivo PID
if (Test-Path $pidFile) {
    try {
        # Ler PID do arquivo
        $pid = Get-Content $pidFile -ErrorAction Stop
        
        Write-Host "📁 PID encontrado no arquivo: $pid" -ForegroundColor Yellow
        
        # Verificar se o processo ainda existe
        $process = Get-Process -Id $pid -ErrorAction SilentlyContinue
        
        if ($process) {
            Write-Host "🔍 Processo encontrado: $($process.ProcessName) (PID: $pid)" -ForegroundColor Green
            
            # Tentar parar graciosamente
            Write-Host "⏳ Tentando parar graciosamente..." -ForegroundColor Yellow
            $process.CloseMainWindow()
            
            # Aguardar 5 segundos
            Start-Sleep -Seconds 5
            
            # Verificar se ainda está rodando
            $process = Get-Process -Id $pid -ErrorAction SilentlyContinue
            if ($process) {
                Write-Host "⚠️  Processo ainda rodando. Forçando encerramento..." -ForegroundColor Yellow
                Stop-Process -Id $pid -Force
                Write-Host "✅ Processo forçadamente encerrado." -ForegroundColor Green
            } else {
                Write-Host "✅ Processo encerrado graciosamente." -ForegroundColor Green
            }
        } else {
            Write-Host "⚠️  Processo com PID $pid não encontrado (já encerrado)." -ForegroundColor Yellow
        }
        
        # Remover arquivo PID
        Remove-Item $pidFile -Force
        Write-Host "🗑️  Arquivo PID removido." -ForegroundColor Gray
        
    } catch {
        Write-Host "❌ Erro ao ler arquivo PID: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "📁 Arquivo PID não encontrado." -ForegroundColor Yellow
    Write-Host "🔍 Procurando processos Python com extractor..." -ForegroundColor Yellow
    
    # Procurar processos Python que podem ser o extractor
    $pythonProcesses = Get-Process -Name "python" -ErrorAction SilentlyContinue
    
    if ($pythonProcesses) {
        Write-Host "🐍 Processos Python encontrados:" -ForegroundColor Green
        foreach ($proc in $pythonProcesses) {
            try {
                $commandLine = (Get-WmiObject Win32_Process -Filter "ProcessId = $($proc.Id)").CommandLine
                if ($commandLine -like "*extractor.py*") {
                    Write-Host "  ✅ Encontrado extractor: PID $($proc.Id)" -ForegroundColor Green
                    Write-Host "  ⏳ Encerrando..." -ForegroundColor Yellow
                    Stop-Process -Id $proc.Id -Force
                    Write-Host "  ✅ Processo $($proc.Id) encerrado." -ForegroundColor Green
                }
            } catch {
                # Ignorar erros de acesso
            }
        }
    } else {
        Write-Host "❌ Nenhum processo Python encontrado." -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "🔍 Verificação final..." -ForegroundColor Yellow

# Verificar se ainda há processos do extractor rodando
$remainingProcesses = Get-Process -Name "python" -ErrorAction SilentlyContinue | Where-Object {
    try {
        $commandLine = (Get-WmiObject Win32_Process -Filter "ProcessId = $($_.Id)").CommandLine
        return $commandLine -like "*extractor.py*"
    } catch {
        return $false
    }
}

if ($remainingProcesses) {
    Write-Host "⚠️  Ainda há processos do extractor rodando:" -ForegroundColor Yellow
    foreach ($proc in $remainingProcesses) {
        Write-Host "  - PID: $($proc.Id)" -ForegroundColor Red
    }
} else {
    Write-Host "✅ Nenhum processo do extractor encontrado." -ForegroundColor Green
}

Write-Host ""
Write-Host "🛑 Operação de parada concluída." -ForegroundColor Red
Write-Host "=================================" -ForegroundColor Cyan

Write-Host ""
Write-Host "Pressione qualquer tecla para sair..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
